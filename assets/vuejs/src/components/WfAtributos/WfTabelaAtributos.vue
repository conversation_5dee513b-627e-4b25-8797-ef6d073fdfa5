<template>
  <div>
    <loading v-if="isLoading" :active.sync="isLoading" :is-full-page="fullPage">
    </loading>
    <div class="table-atribute" v-if="!isLoading">
      <table class="table table-striped">
        <thead class="fixed-header">
          <tr>
            <th class="dynamic-column dynamic-column-description" style="z-index: 1001">
              <input class="form-check-input" type="checkbox" v-model="selectAll" @change="selectAllCheckboxes" />
            </th>
            <th class="dynamic-column dynamic-column-description" style="z-index: 1001">
              Part number
            </th>
            <th class="dynamic-column dynamic-column-description" style="z-index: 1001">
              <div class="col-description">
                <span>Descrição</span>
                <button class="btn btn-icon" @click="toggleDescriptions">
                  <i class="glyphicon" :class="expandDescription
                    ? 'glyphicon-chevron-left'
                    : 'glyphicon-chevron-right'
                    "></i>
                </button>
              </div>
            </th>
            <th class="dynamic-column dynamic-column-description" style="z-index: 1001">
            </th>
            <th v-for="attr in dadosAtributos.lista" :key="attr.id" class="dynamic-column" style="position: sticky">
              <div class="dynamic-column-th">
                <button v-if="
                  movimentar_itens ||
                  permissionType == visionViewEnum.FILL_HOMOLOG_VIEW ||
                  preencherAtributos
                " :disabled="selectedItems.length == 0" class="btn btn-icon-fill-lot"
                  @click="openModalFillInLot(attr)">
                  <i class="glyphicon glyphicon-list"></i>;
                </button>
                <span class="tooltip-trigger" data-toggle="tooltip" data-html="true"
                  :data-original-title="attr.nomeApresentacao ? `<strong> Descrição: </strong> ${attr.nomeApresentacao}` : ''"
                  :style="{
                    cursor: 'help',
                    color: attr.obrigatorio ? 'yellow' : 'inherit',
                  }" @mouseup="refreshTooltip($event.target, attr)">
                  {{ truncateText(attr.nomeApresentacao, 50) || 'Sem atributos' }}
                  <span v-if="attr.obrigatorio">*</span>
                </span>
                <ProductColorLegend :data="attr.objetivos"></ProductColorLegend>

                <TooltipCustom v-if="attr.nomeApresentacao" :title="showInfo(attr)" style="cursor: help">
                  <i class="glyphicon glyphicon-info-sign"></i>
                </TooltipCustom>
              </div>
            </th>
            <th style="z-index: 1001; width: 85px" class="sticky-actions dynamic-column-actions">
              Ações
            </th>
          </tr>
        </thead>

        <tbody v-for="(item, rowIndex) in dadosAtributos.itens" :key="item.id_item"
          :class="{ 'odd-item': rowIndex % 2 === 0 }" v-memo="[item.id_item]">
          <tr :data-id="item.id_item" :data-part_number="item.part_number">
            <td class="text-center">
              <input class="form-check-input" type="checkbox" v-model="selectedItems" :value="item.id_item" />
            </td>
            <td>
              <strong>{{ item.part_number }}</strong>
              <i v-show="item.status_wf == 'em_revisao'" data-toggle="tooltip" title="Em revisão"
                style="cursor: help; color: #af0000; z-index: 1005" class="glyphicon glyphicon-exclamation-sign"></i>
            </td>

            <td> {{ item.descricao_mercado_local ? item.descricao_mercado_local : item.descricao_curta }}
            </td>

            <td>
              <TooltipCustom :title="showInfo2(item)" style="cursor: help;">
                <i class="glyphicon glyphicon-info-sign"></i>
              </TooltipCustom>
            </td>

            <td v-for="(attr, cellIndex) in dadosAtributos.lista" rowspan="2"
              v-memo="[item.id_item + '_' + (attr && (attr.codigo || attr.id))]"
              :key="item.id_item + '_' + (attr && (attr.codigo || attr.id))">
              <WfTabelaAtributoInput v-if="attr && !attr.codigo_pai" :id="item.id_item + '_' + attr.codigo"
                :initialAttr="attr" :initialValue="formatInitialValue(attr, item.id_item) || ''" :idItem="item.id_item"
                :permissionType="permissionType" :homologar_atributos="homologarAtributos"
                :preencher_atributos="preencherAtributos" :movimentar_itens="movimentarItens"
                :diana_atributos="diana_atributos" @handleNewColumns="
                  handleNewColumns(rowIndex, cellIndex, $event)
                  " @dataEdited="handleDataEdited" @changedValues="changedValues"></WfTabelaAtributoInput>

              <WfTabelaAtributoInput v-if="
                attr &&
                attr.codigo_pai &&
                showCellInColumn(item.id_item, attr)" :key="item.id_item + '_' + attr.codigo"
                :id="item.id_item + '_' + attr.codigo" :initialAttr="attr"
                :initialValue="formatInitialValue(attr, item.id_item) || ''" :idItem="item.id_item"
                :permissionType="permissionType" @handleNewColumns="
                  handleNewColumns(rowIndex, cellIndex, $event)
                  " @dataEdited="handleDataEdited" :preencher_atributos="preencherAtributos"
                :diana_atributos="diana_atributos" @changedValues="changedValues"></WfTabelaAtributoInput>

              <div v-if="homologar_atributos && !attr.dbdata[item.id_item]">
                {{ '(vazio)' }}
              </div>
            </td>

            <td class="sticky-actions" rowspan="2">
              <div class="centralize">
                <button class="btn btn-icon" @click="openModalHistory(item)">
                  <i class="glyphicon" :class="movimentar_itens ? 'glyphicon-time' : 'glyphicon-check'
                    "></i>
                </button>
              </div>
            </td>
          </tr>
          <tr v-if="expandDescription">
            <td colspan="4" class="sticky-actions">
              <div class="complete-description">
                <hr />
                <span>
                  {{ item.descricao_completa }}
                </span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div>
      <button :disabled="isLoadMore()" @click="loadMoreItens" type="button"
        class="btn btn-lg btn-block btn-default mt-5">
        Carregar mais resultados - {{ itemsPerPage }} itens
        <div v-if="isLoadingMore" class="spinner-border"
          style="border: 2px solid black; border-right-color: transparent">
          <span class="sr-only">Loading...</span>
        </div>
      </button>
    </div>
    <div class="d-flex buttons-actions-container mt-5">
      <button @click="openModalDiscart" type="button" class="btn btn-default">
        Voltar
      </button>

      <div>
        <button :disabled="selectedItems.length == 0"
          v-if="this.diana_atributos && this.dadosAtributos.lista[0].codigo != 'null'" @click="integraDiana"
          type="button" class="btn btn-primary">
          Sugestão Diana dos Selecionados
        </button>
        <button :disabled="selectedItems.length == 0 || this.isNacional"
          v-if="this.movimentarItens || permissionType == visionViewEnum.FILL_HOMOLOG_VIEW"
          @click="openModalMoveSelecteds" type="button" class="btn btn-primary">
          Alterar Status de itens selecionados
        </button>
        <button :disabled="selectedItems.length == 0"
          v-if="this.homologarAtributos || permissionType == visionViewEnum.FILL_HOMOLOG_VIEW" type="button"
          class="btn btn-primary" @click="openModalHomologSelecteds(false, true)">
          Homologar selecionados
        </button>
        <button v-if="this.preencherAtributos || permissionType == visionViewEnum.FILL_HOMOLOG_VIEW"
          :disabled="saveAttr" @click="save" type="button" class="btn btn-success">
          <span v-if="saveAttr" class="spinner-border">
            <span class="sr-only">Loading...</span>
          </span>
          {{ saveAttr ? 'Salvando...' : 'Salvar' }}
        </button>
      </div>
    </div>
    <ModalActions v-if="modalHistoryItem" @closeModalHistory="closeModalHistory" :modalItem="modalHistoryItem"
      :permissionType="permissionType" :homologar_atributos="homologarAtributos"
      :preencher_atributos="preencherAtributos" :movimentar_itens="movimentarItens"
      :permite_homologar="permite_homologar" :listItens="listItens" :homologarSemObrigatorios="homologarSemObrigatorios"
      @openModalHomologSelecteds="openModalHomologSelecteds"
      :hasObrigatoriosVazio="modalHomologSelectedItem.items.has_obrigatorio_vazio"></ModalActions>
    <ModalFillInLot v-if="modalFillInLotItem" :modalItems="modalFillInLotItem" @closeModal="closeModalFillInLot"
      @handleFillInLot="handleFillInLot">
    </ModalFillInLot>
    <ModalMoveSelecteds v-if="modalMoveSelectedNcm" :ncmId="modalMoveSelectedNcm" @closeModal="closeModalMoveSelecteds"
      :selectedItems="getSelectedItemIds()"></ModalMoveSelecteds>
    <ModalHomologSelecteds v-if="modalHomologSelectedItem" :modalItem="modalHomologSelectedItem" :ncmId="ncmId"
      @closeModal="closeModalHomologSelecteds" @listItensReady="handleListItensReady"
      :hasObrigatoriosVazio="modalHomologSelectedItem.items.has_obrigatorio_vazio"
      :homologarSemObrigatorios="homologarSemObrigatorios"></ModalHomologSelecteds>

    <!-- Footer de seleção em massa para NCM -->
    <BulkSelectionFooter :selectedItems="selectedItems" :totalSelectedItems="selectedItems.length"
      :totalGeneralItems="totalItems" :totalAvailableCount="1" :canShow="selectedItems.length > 0"
      :showSelectAllAction="selectedItems.length < totalItems" :showCustomActions="true" :isLoading="isLoadingAllItems"
      :hasLoadedAll="hasSelectedAllItems" itemLabel="item" countLabel="NCM" @selectAll="selectAllItemsFromNCM"
      @clearSelection="clearItemSelection">
    </BulkSelectionFooter>
  </div>
</template>

<style scoped>
/* Estilos para links desabilitados no footer */
.footer-action-link.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}
</style>

<script>
import axios from 'axios';
import 'vue-loading-overlay/dist/vue-loading.css';
import Loading from 'vue-loading-overlay';
import { defineAsyncComponent } from 'vue';
import ModalMoveSelecteds from './components/ModalMoveSelecteds.vue';
import ModalFillInLot from './components/modalFillInLot/ModalFillInLot.vue';
import ModalActions from './components/modalActions/ModalActions.vue';
import ModalHomologSelecteds from './components/ModalHomologSelecteds.vue';
import ProductColorLegend from './components/ProductColorLegend.vue';
import TooltipCustom from './components/TooltipCustom.vue';
import BulkSelectionFooter from './components/BulkSelectionFooter.vue';

const VisionViewEnum = Object.freeze({
  HOMOLOG_VIEW: 'homolog_view',
  FILL_VIEW: 'fill_view',
  FILL_HOMOLOG_VIEW: 'fill_homolog_view',
});

export default {
  components: {
    Loading,
    WfTabelaAtributoInput: defineAsyncComponent(() =>
      import('./WfTabelaAtributoInput.vue')
    ),
    ModalActions,
    ModalMoveSelecteds,
    ModalFillInLot,
    ModalHomologSelecteds,
    ProductColorLegend,
    TooltipCustom,
    BulkSelectionFooter,
  },
  data() {
    return {
      isNacional: false,
      hasBeenEditedAttr: false,
      expandDescription: false,
      selectedItems: [],
      selectedAttributes: {},
      selectAll: false,
      isLoading: false,
      fullPage: true,
      ncmId: null,
      dadosAtributos: { lista: [], itens: [] },
      isLoadingMore: false,
      currentPage: 0,
      qtdAddPage: 10,
      totalItems: 0,
      visionViewEnum: VisionViewEnum,
      permissionType: VisionViewEnum.HOMOLOG_VIEW,
      modalHistoryItem: null,
      modalFillInLotItem: null,
      modalMoveSelectedNcm: null,
      modalHomologSelectedItem: null,
      homologarAtributos: null,
      preencherAtributos: null,
      movimentarItens: null,
      saveAttr: false,
      diana_atributos: null,
      integra_diana_company: false,
      email_usuario: '',
      attrs_processados_diana: [],
      registro_item: '',
      // Propriedades para seleção em massa
      isLoadingAllItems: false,
      hasSelectedAllItems: false,
      allItemsFromNCM: [], // Todos os itens da NCM (carregados e não carregados)
    };
  },
  watch: {
    saveData() {
      $('#ModalDiscart').modal('hide');
      this.save();
    },
    itemsPerPage() {
      // Quando o itemsPerPage muda, recarregar os dados
      this.currentPage = 0;
      this.loadData();
    },
  },
  props: {
    listItens: [],
    data: {
      required: true,
      default: String,
    },
    totalqtdItems: {
      required: true,
      default: Number,
    },
    saveData: {
      required: false,
      default: String,
    },
    homologarSemObrigatorios: {
      required: false,
      default: Boolean | Number
    },
    itemsPerPage: {
      required: false,
      type: Number,
      default: 10
    }
  },
  beforeMount() {
    this.ncmId = this.data;
    this.totalItems = this.totalqtdItems;
  },
  mounted() {
    this.loadData();
    this.$nextTick(() => {
      this.initializeTooltips();
    });
    this.hasBeenEditedAttr = false;

    axios
      .get('/wf/atributos/get_data_company', {
        diana_atributos: true
      })
      .then((response) => {
        this.integra_diana_company = response.data.integra_diana_company;
        this.email_usuario = response.data.email_usuario;
      })
      .catch((err) => {
        this.integra_diana_company = false;
        this.email_usuario = response.email_usuario;
      });


    let tipoSelecionado = $('input[name="tipo_item"]:checked').val();

    if (tipoSelecionado == 'nacional') {
      this.isNacional = true;
    } else {
      this.isNacional = false;
    }

  },
  updated() {
    this.$nextTick(() => {
      this.initializeTooltips();
    });
  },
  computed: {
    computedVisibility() {
      return this.isVisible(this.someParameter);
    },
    // Computed para verificar se Diana pode ser usado com a seleção atual
    canUseDianaForSelection() {
      if (this.selectedItems.length === 0) return false;

      // Se selecionou todos os itens da NCM, mas nem todos estão carregados, Diana não pode ser usado
      if (this.hasSelectedAllItems && this.selectedItems.length > this.dadosAtributos.itens.length) {
        return false;
      }

      // Verificar se todos os itens selecionados estão na lista carregada
      const loadedItemIds = this.dadosAtributos.itens.map(item => item.id_item);
      return this.selectedItems.every(itemId => loadedItemIds.includes(itemId));
    },
    filteredAtributos() {
      return this.dadosAtributos.lista.filter(attr => attr && (attr.codigo || attr.codigo_pai));
    },
  },
  methods: {
    /**
     * Atualiza o valor de um selectpicker dentro de um container.
     * @param {string} containerId - ID do container que cont m o selectpicker.
     * @param {string|number} value - Novo valor a ser setado no selectpicker.
     */
    updateBootstrapSelect(containerId, value) {
      this.$nextTick(() => {
        const $select = $(`#${containerId} select`);
        if ($select.length > 0 && typeof $select.selectpicker === 'function') {
          // Atualiza o valor do select nativo
          $select.val(value.toString());
          // Atualiza o valor visual do selectpicker
          $select.selectpicker('val', value.toString());
          $select.selectpicker('refresh');
          // Dispara eventos para garantir atualização do Vue e plugins
          $select.trigger('change');
          $select.trigger('input');
        }
      });
    },
    applyTooltips() {
      // Destruir tooltips existentes primeiro
      $('[data-toggle="tooltip"]').tooltip('destroy');

      // Reinicializar tooltips com dados atualizados
      $('[data-toggle="tooltip"]').tooltip({
        container: 'body',
        placement: 'auto',
        boundary: 'window',
        html: true,
        trigger: 'hover',
      });
    },

    handleListItensReady(listItens) {
      this.listItens = listItens;
    },
    handleDataEdited(value) {
      this.hasBeenEditedAttr = value;
      this.$emit('dataEdited', value);
    },
    async loadData() {
      this.isLoading = true;
      try {
        const response = await axios.post(`/wf/atributos/get_data_attr`, {
          ncm: this.ncmId,
          items_per_page: this.itemsPerPage,
        });

        const data = response.data;

        if (data.itens.length == 0) {
          window.location.reload();
        }

        this.permissionType = data.permissionType;
        this.dadosAtributos = data;

        this.homologarAtributos = data.homologar_atributos;
        this.preencherAtributos = data.preencher_atributos;
        this.movimentarItens = data.movimentar_itens;
        this.diana_atributos = data.diana_atributos;
        let tipoSelecionado = $('input[name="tipo_item"]:checked').val();

        if (tipoSelecionado == 'nacional') {
          this.isNacional = true;
          this.movimentarItens = false;
        } else {
          this.isNacional = false;
        }

        this.currentPage = this.itemsPerPage;
      } catch (error) {
        console.error('Erro na requisição:', error);
      } finally {
        this.isLoading = false;
      }
    },
    truncateText(text, maxLength) {
      if (text && text.length > maxLength) {
        return text.substring(0, maxLength) + '...';
      } else {
        return text;
      }
    },
    /**
     * Formata o valor inicial de um atributo com base em seu tipo (principal ou filho)
     * e relacionamentos. Este método é usado para inicializar valores em inputs da tabela.
     * 
     * @param {Object} attr - Objeto do atributo contendo suas propriedades e configurações
     * @param {string|number} itemId - Identificador único do item
     * @returns {string} Valor formatado do atributo ou string vazia se não encontrado
     */
    formatInitialValue(attr, itemId) {
      if (!attr) return '';

      if (!attr.dbdata) {
        this.$set(attr, 'dbdata', {});
      }

      if (!attr.dbdata[itemId]) {
        this.$set(attr.dbdata, itemId, { codigo: '' });
      }

      return attr.dbdata[itemId].codigo;
    },
    /**
     * Recupera o valor de um atributo filho baseado em seu relacionamento com o atributo pai.
     * Atributos filhos são encontrados dentro dos condicionados do atributo pai.
     * 
     * @param {Object} attr - Objeto do atributo filho
     * @param {string|number} itemId - Identificador único do item
     * @returns {string} Valor do atributo filho ou string vazia se não encontrado
     * 
     * @example
     * // Estrutura esperada do atributo pai:
     * // parentAttr = {
     * //   codigo: "PAI_1",
     * //   condicionados: [{
     * //     atributo: {
     * //       codigo: "FILHO_1",
     * //       dbdata: { "item1": { codigo: "valor1" } }
     * //     }
     * //   }]
     * // }
     */
    getChildAttributeValue(attr, itemId) {
      // Busca o atributo pai na lista de atributos
      const parentAttr = this.dadosAtributos.lista.find(a => a.codigo === attr.codigo_pai);

      // Verifica se o pai existe e possui condicionados
      if (!parentAttr || !parentAttr.condicionados) {
        return '';
      }

      // Busca o atributo filho específico nos condicionados do pai
      const condicionado = parentAttr.condicionados.find(c =>
        c.atributo.codigo === attr.codigo
      );

      // Verifica se o condicionado existe e possui os dados necessários
      if (!condicionado || !condicionado.atributo || !condicionado.atributo.dbdata) {
        return '';
      }

      // Retorna o código do valor armazenado para o item específico
      return condicionado.atributo.dbdata[itemId] && condicionado.atributo.dbdata[itemId].codigo || '';
    },
    /**
     * Recupera o valor de um atributo principal diretamente de seus dados.
     * Atributos principais são aqueles que não possuem relacionamento pai-filho.
     * 
     * @param {Object} attr - Objeto do atributo principal
     * @param {string|number} itemId - Identificador único do item
     * @returns {string} Valor do atributo principal ou string vazia se não encontrado
     * 
     * @example
     * // Estrutura esperada do atributo:
     * // attr = {
     * //   dbdata: {
     * //     "item1": { codigo: "valor1" }
     * //   }
     * // }
     */
    getMainAttributeValue(attr, itemId) {
      if (!attr.dbdata || !attr.dbdata[itemId]) {
        return '';
      }

      return attr.dbdata[itemId].codigo || '';
    },
    logAndReturn(attr, itemId) {
      const value = this.formatInitialValue(attr, itemId) || '';
      console.log(`initialValue for ${itemId}_${attr.codigo}:`, value);
      return value;
    },
    toggleDescriptions() {
      this.expandDescription = !this.expandDescription;
    },
    selectAllCheckboxes() {
      if (this.selectAll) {
        this.selectedItems = this.dadosAtributos.itens.map(
          (item) => item.id_item
        );
      } else {
        this.selectedItems = [];
      }
    },

    // Método para limpar seleção (usado pelo BulkSelectionFooter)
    clearItemSelection() {
      this.selectedItems = [];
      this.selectAll = false;
      this.hasSelectedAllItems = false;
      this.allItemsFromNCM = [];
    },

    // Método para selecionar todos os itens da NCM (mesmo os não carregados)
    async selectAllItemsFromNCM() {
      if (this.isLoadingAllItems) return;

      this.isLoadingAllItems = true;

      try {
        // Fazer requisição para buscar todos os IDs dos itens da NCM. Passar como parâmetro o tipo de item (importado ou nacional)
        const response = await axios.get(`atributos/ajax_get_all_items_from_ncm/${this.ncmId}/${this.isNacional ? 'nacional' : 'importado'}`);

        if (response.data.err === 0) {
          this.allItemsFromNCM = response.data.items || [];
          this.selectedItems = this.allItemsFromNCM.map(item => item.id_item);
          this.hasSelectedAllItems = true;
          this.selectAll = true;
        } else {
          throw new Error(response.data.msg || 'Erro ao carregar itens da NCM');
        }
      } catch (error) {
        console.error('Erro ao carregar todos os itens da NCM:', error);
        swal({
          title: "Erro!",
          text: "Não foi possível carregar todos os itens da NCM. Tente novamente.",
          type: "error",
        });
      } finally {
        this.isLoadingAllItems = false;
      }
    },
    verificarCamposObrigatorios() {
      const camposObrigatorios = document.querySelectorAll('input[required], select[required]');
      const nomesCamposVazios = new Set();
      const mensagensDeErro = [];

      camposObrigatorios.forEach(campo => {
        const apresentacao = campo.dataset.apresentacao || 'não definido';
        if (!campo.value.trim()) {
          if (!nomesCamposVazios.has(apresentacao)) {
            mensagensDeErro.push(`Campo obrigatório <b>'${apresentacao}'</b> está vazio. <br>`);
            nomesCamposVazios.add(apresentacao); // Marca o nome como já processado
          }
        }
      });

      if (mensagensDeErro.length > 0) {
        const mensagensFormatadas = mensagensDeErro.join('');
        swal("Erro!", `Os seguintes campos obrigatórios não foram preenchidos: <br>${mensagensFormatadas}`, "error");
        return false;
      }

      if (mensagensDeErro.length > 0) {
        const mensagensFormatadas = mensagensDeErro.join('');
        swal("Erro!", `Os seguintes campos obrigatórios não foram preenchidos: <br>${mensagensFormatadas}`, "error");
        return false;
      }

      return true;
    },
    async save() {

      this.isLoading = true;
      this.hasBeenEditedAttr = false;
      this.$emit("dataEdited", false);
      try {
        this.saveAttr = true;

        // Dividir os dados em lotes menores
        const batchSize = 50;
        const totalBatches = Math.ceil(this.dadosAtributos.itens.length / batchSize);

        for (let i = 0; i < totalBatches; i++) {
          const startIdx = i * batchSize;
          const endIdx = Math.min((i + 1) * batchSize, this.dadosAtributos.itens.length);

          const batchItens = this.dadosAtributos.itens.slice(startIdx, endIdx);

          // Enviar apenas um lote por vez
          await axios.post(`atributos/save_attrs`, {
            raw_attrs: this.dadosAtributos.lista,
            itens: batchItens,
          });

          // Dar tempo para o navegador respirar
          await new Promise(resolve => setTimeout(resolve, 100));
        }

        this.isLoading = false;
        this.saveAttr = false;

        swal({
          title: "Sucesso!",
          text: "Atributos salvos com sucesso!",
          type: "success",
        });
      } catch (e) {
        console.error(e);
        this.isLoading = false;
        this.saveAttr = false;
      }
    },
    /**
     * Manipula as mudanças de valores dos atributos, tanto principais quanto filhos
     * @param {Object} payload - Contém o atributo e ID do item modificado
     * @param {Object} payload.attr - Atributo que foi modificado
     * @param {string|number} payload.itemId - ID do item que teve o valor alterado
     */
    changedValues({ attr, itemId }) {
      if (!attr || !attr.codigo) return;

      // Se for um atributo filho
      if (attr.codigo_pai) {
        const parentIndex = this.dadosAtributos.lista.findIndex(
          item => item.codigo === attr.codigo_pai
        );

        if (parentIndex !== -1) {
          const parent = this.dadosAtributos.lista[parentIndex];
          const childIndex = parent.condicionados.findIndex(
            c => c.atributo.codigo === attr.codigo
          );

          if (childIndex !== -1) {
            // Atualiza o dbdata do atributo filho
            if (!parent.condicionados[childIndex].atributo.dbdata) {
              this.$set(parent.condicionados[childIndex].atributo, 'dbdata', {});
            }

            this.$set(
              parent.condicionados[childIndex].atributo.dbdata,
              itemId,
              { ...attr.dbdata[itemId] }
            );

            // Força atualização do array para garantir reatividade
            this.dadosAtributos.lista = [...this.dadosAtributos.lista];
          } else {
            console.error("Atributo filho não encontrado:", attr.codigo);
          }
        } else {
          console.error("Atributo pai não encontrado:", attr.codigo_pai);
        }
      } else {
        // Se for um atributo principal
        const index = this.dadosAtributos.lista.findIndex(
          item => item.codigo === attr.codigo
        );

        if (index !== -1) {
          if (!this.dadosAtributos.lista[index].dbdata) {
            this.$set(this.dadosAtributos.lista[index], 'dbdata', {});
          }

          this.$set(this.dadosAtributos.lista[index], 'dbdata', { ...attr.dbdata });

          // Força atualização do array para garantir reatividade
          this.dadosAtributos.lista = [...this.dadosAtributos.lista];
        } else {
          console.error("Atributo não encontrado:", attr.codigo);
        }
      }

      // Marca que houve alterações
      // this.hasBeenEditedAttr = true;
      // this.$emit("dataEdited", true);
    },
    isLoadMore() {
      console.log('this.currentPage', parseInt(this.currentPage));
      console.log('this.totalItems', this.totalItems);
      if (parseInt(this.currentPage) >= parseInt(this.totalItems)) {
        return true;
      }
      if (this.isLoadingMore) return true;
      return false;
    },
    // Métodos auxiliares para atualização de atributos
    // Atualiza os dados de um atributo neto
    updateGrandchildData(neto, matchingGrandchild) {
      if (!neto.atributo.dbdata) {
        neto.atributo.dbdata = {};
      }

      if (matchingGrandchild && matchingGrandchild.atributo.dbdata) {
        Object.entries(matchingGrandchild.atributo.dbdata).forEach(([itemId, itemData]) => {
          if (itemData && itemData.codigo && itemData.codigo !== '') {
            this.$set(neto.atributo.dbdata, itemId, itemData);
          }
        });
      }
    },

    // Atualiza os dados de um atributo filho e seus netos
    updateChildData(condicionado, matchingChild) {
      if (!condicionado.atributo.dbdata) {
        condicionado.atributo.dbdata = {};
      }

      if (matchingChild && matchingChild.atributo.dbdata) {
        // Atualiza dados do filho
        Object.entries(matchingChild.atributo.dbdata).forEach(([itemId, itemData]) => {
          if (itemData && itemData.codigo && itemData.codigo !== '') {
            this.$set(condicionado.atributo.dbdata, itemId, itemData);
          }
        });

        // Atualiza netos se existirem
        if (condicionado.atributo.condicionados &&
          condicionado.atributo.condicionados.length > 0) {
          condicionado.atributo.condicionados.forEach(neto => {
            const matchingGrandchild = matchingChild.atributo.condicionados &&
              matchingChild.atributo.condicionados.find(
                respGrandchild => respGrandchild.atributo.codigo === neto.atributo.codigo
              );

            this.updateGrandchildData(neto, matchingGrandchild);
          });
        }
      }
    },
    // updateChildData(condicionado, matchingChild) {
    //   if (matchingChild && matchingChild.atributo.dbdata) {
    //     Object.entries(matchingChild.atributo.dbdata).forEach(([itemId, itemData]) => {
    //       if (itemData && itemData.codigo && itemData.codigo !== '') {
    //         this.$set(condicionado.atributo.dbdata, itemId, itemData);
    //       }
    //     });
    //   }

    //   this.$nextTick(() => {
    //     this.$forceUpdate();
    //   });
    // },
    // Atualiza os dados do atributo principal e seus filhos
    updateAttributeData(responseAttr, existingAttrIndex) {
      const currentDbData = this.dadosAtributos.lista[existingAttrIndex].dbdata;
      const hasChildren = this.dadosAtributos.lista[existingAttrIndex].condicionados.length > 0;

      // Atualiza filhos se existirem
      if (hasChildren) {
        this.dadosAtributos.lista[existingAttrIndex].condicionados.forEach(condicionado => {
          if (condicionado && condicionado.atributo) {
            const matchingChild = responseAttr.condicionados.find(
              respChild => respChild.atributo.codigo === condicionado.atributo.codigo
            );

            this.updateChildData(condicionado, matchingChild);
          }
        });
      }

      // Atualiza dados do atributo principal
      Object.entries(responseAttr.dbdata || {}).forEach(([itemId, itemData]) => {
        if (itemData && itemData.codigo && itemData.codigo !== '') {
          this.$set(currentDbData, itemId, itemData);
        }
      });
    },

    // Atualiza os dados da resposta
    updateResponseData(response) {
      response.data.lista.forEach(responseAttr => {
        const existingAttrIndex = this.dadosAtributos.lista.findIndex(
          attr => attr.codigo === responseAttr.codigo
        );

        if (existingAttrIndex !== -1) {
          this.updateAttributeData(responseAttr, existingAttrIndex);
        }
      });

      this.dadosAtributos.itens = this.dadosAtributos.itens.concat(response.data.itens);

      // Atualiza permissões e estados
      this.updatePermissionsAndState(response.data);
    },

    // Atualiza permissões e estado
    updatePermissionsAndState(data) {
      this.permissionType = data.permissionType;
      this.homologarAtributos = data.homologar_atributos;
      this.preencherAtributos = data.preencher_atributos;
      this.movimentarItens = data.movimentar_itens;

      this.diana_atributos = data.diana_atributos;

      if (this.currentPage < this.totalItems) {
        this.currentPage += this.itemsPerPage;
      }
    },

    // Método principal loadMoreItens refatorado
    async loadMoreItens() {
      this.isLoadingMore = true;
      try {
        const response = await axios.post(`/wf/atributos/get_data_attr`, {
          ncm: this.ncmId,
          per_page: this.currentPage,
          items_per_page: this.itemsPerPage,
        });

        console.log(this.currentPage, 'this.currentPage TESTE');

        this.updateResponseData(response);
      } catch (error) {
        console.error("Erro na requisição:", error);
      } finally {
        this.isLoadingMore = false;
      }
    },
    openModalDiscart() {
      this.$emit('openModalDiscart');
    },

    formatDate(date) {
      return moment(date).format('DD/MM/YYYY');
    },
    getDescricaoById(id) {
      switch (id) {
        case '1':
          return "Homologar";
        case '2':
          return "Homologado";
        case '3':
          return "Reprovado";
        case '4':
          return "Inativo";
        case '5':
          return "Em Revisão";
        case '6':
          return "Em Análise";
        case '7':
          return "Pendente de Informações";
        case '8':
          return "Perguntas Respondidas";
        case '9':
          return "Revisar Informações ERP";
        case '10':
          return "Homologado em Revisão";
        case '11':
          return "Revisar Informações Técnicas";
        case '12':
          return "Informações ERP Revisadas";
        case '13':
          return "Aguardando definição responsável";
        case '14':
          return "Aguardando Descrição";
        case '15':
          return "Perguntas Respondidas (Novas)";
        default:
          return "sem descrição";
      }
    },
    showInfo2(item) {
      let descricao_fiscal = this.getDescricaoById(item.status_fiscal);
      let descricao_atributos = '';
      let cor_atributo = '';

      if (item.status_wf_descricao) {
        descricao_atributos = item.status_wf_descricao;
        cor_atributo = item.color;
      } else {
        descricao_atributos = 'Item nacional';
        cor_atributo = '#9510AC';
      }

      if (descricao_fiscal == 'Homologar') {
        descricao_fiscal = 'Pendente de Homologação';
      }
      // conteúdo do tooltip
      let result = `
        <div style="text-align:left; padding: 5px 10px; font-size: 12px">
          <div style="margin-bottom:5px;">
            <strong style="color: white">Status de classificação fiscal:</strong>
            <span style="font-weight:300; color: white"><br>${descricao_fiscal}</span>
          </div>
          <div style="margin-bottom:5px;">
            <br>
            <strong style="color: white">Status de atributos:</strong>
      </div>
        </div>
        <div >
         
          <span 
            class="label" 
            style="background-color: ${cor_atributo}; color: #fff; padding: 4px 8px; border-radius: 4px; font-size: 12px; display: inline-block;" 
            data-toggle="tooltip" 
            title="" 
            data-original-title="${descricao_atributos}">
            ${descricao_atributos}
          </span>
        </div>
      `;

      return result;
    },
    showInfo(attr) {
      const colors = {
        Produto: '#EA3C3B',
        LPCO: '#138EF2',
        Duimp: '#CC52C7',
        'Tratamento administrativo': '#00E73D',
        'Cadastro de intervenientes': '#8EE8DD',
        'Tratamento tributário': '#FFB325',
      }

      var result =
        '<div style="text-align:left; padding: 5px 10px; font-size: 12px">';

      if (attr.codigo) {
        result += `<div style="margin-bottom:5px;"><strong style="color: white"> Codigo: </strong> <span style="font-weight:300; color: white"> ${attr.codigo}</span></div>`;
      }
      if (attr.modalidade) {
        result += `<div style="margin-bottom:5px;"><strong style="color: white"> Modalidade: </strong> <span style="font-weight:300; color: white"> ${attr.modalidade}</span> </div>`;
      }
      if (attr.dataInicioVigencia) {
        result += `<div style="margin-bottom:5px;"><strong style="color: white"> Vigência: </strong> <span style="font-weight:300; color: white"> ${this.formatDate(
          attr.dataInicioVigencia
        )} ${attr.dataFimVigencia
          ? 'até ' + this.formatDate(attr.dataFimVigencia)
          : 'até indefinido'
          }</span> </div>`;
      }
      if (attr.orgaos) {
        result += `<div style="margin-bottom:5px;"><strong style="color: white"> Orgãos: </strong> <span style="font-weight:300; color: white"> ${attr.orgaos.toString()}</span> </div>`;
      }
      if (attr.objetivos) {
        result += `<strong style="color: white">Objetivos:</strong><br>`;
        attr.objetivos.forEach((objetivo) => {
          result += `<span style="color:${colors[objetivo.descricao]}">- ${objetivo.descricao
            }</span><br>`;
        });

        // Atualizamos a lista original usando Vue.set para garantir reatividade
        // this.$set(this.dadosAtributos, 'lista', listaAtualizada);
      }
      if (attr.orientacaoPreenchimento) {
        result += `<strong> Orientacao de preenchimento: </strong> ${attr.orientacaoPreenchimento} <br>`;
      }
      if (attr.formaPreenchimento) {
        result += `<strong> Forma de preenchimento: </strong> ${attr.formaPreenchimento} <br>`;
      }
      if (attr.tamanhoMaximo) {
        result += `<strong> Preenchimento máximo: </strong> ${attr.tamanhoMaximo} <br>`;
      }
      if (attr.casasDecimais) {
        result += `<strong> Casas decimais: </strong> ${attr.casasDecimais} <br>`;
      }
      result += '</div>';

      return result;
    },
    handleNewColumns(rowIndex, cellIndex, { newItens, deleteItens, arrayIsConditions, idItem }) {
      // 1. Mapa para armazenar temporariamente os atributos e seus índices originais
      const originalIndexMap = {};
      this.dadosAtributos.lista.forEach((attr, index) => {
        originalIndexMap[attr.codigo] = index;
      });

      // 2. Processar novos itens
      // Inverter a ordem do array newItens antes de processá-lo
      newItens.reverse().forEach(newItem => {
        const existingIndex = this.dadosAtributos.lista.findIndex(
          attr => attr.codigo === newItem.codigo
        );

        if (existingIndex === -1) {
          // Adicionar novo item na posição correta
          const parentIndex = originalIndexMap[newItem.codigo_pai];

          if (typeof parentIndex !== 'undefined') {
            // Inserir após o pai
            this.dadosAtributos.lista.splice(parentIndex + 1, 0, newItem);
            // Atualizar índices no mapa
            Object.keys(originalIndexMap).forEach(key => {
              if (originalIndexMap[key] > parentIndex) {
                originalIndexMap[key]++;
              }
            });
          }
        }
      });

      // 3. Processar itens para deleção
      if (deleteItens && deleteItens.length > 0) {
        // Verificar se algum outro item ainda precisa dessas colunas
        const columnsToDelete = deleteItens.filter(codigo => {
          const attr = this.dadosAtributos.lista.find(a => a.codigo === codigo);
          if (!attr) return false;

          // Verificar se algum item ainda precisa desta coluna
          const isNeededByAnyItem = Object.values(this.dadosAtributos.itens).some(item => {
            const parentAttr = this.dadosAtributos.lista.find(
              a => a.codigo === attr.codigo_pai
            );
            if (!parentAttr || !parentAttr.dbdata) return false;

            return Object.values(parentAttr.dbdata).some(dbdata => {
              if (!dbdata) return false;
              // Verificar condições específicas que mantêm a coluna
              return this.checkIfColumnIsNeeded(dbdata, attr);
            });
          });

          return !isNeededByAnyItem;
        });

        // Remover apenas as colunas que realmente não são mais necessárias
        columnsToDelete.forEach(codigo => {
          const index = this.dadosAtributos.lista.findIndex(
            attr => attr.codigo === codigo
          );
          if (index !== -1) {
            this.dadosAtributos.lista.splice(index, 1);
          }
        });
      }

      // 4. Atualizar o estado de visibilidade
      this.dadosAtributos.lista.forEach(attr => {
        if (arrayIsConditions[attr.codigo]) {
          this.$set(attr, 'isConditionallyVisible', true);
          this.$set(attr, 'visibilityConditions', arrayIsConditions[attr.codigo]);
        }
      });

      // 5. Forçar a atualização do componente
      this.$nextTick(() => {
        // this.applyTooltips();
        this.$forceUpdate();
      });
    },

    // Método auxiliar para verificar se uma coluna ainda é necessária
    checkIfColumnIsNeeded(dbdata, attr) {
      if (!dbdata || !attr) return false;

      // Implementar aqui a lógica específica para verificar se o dbdata
      // indica que a coluna ainda é necessária
      // Por exemplo, verificar condições, valores preenchidos, etc.

      return false; // ou true, dependendo da sua lógica específica
    },

    showCellInColumn(itemId, attribute) {
      let parentAttr = this.dadosAtributos.lista.find((value) => {
        if (value.codigo) {
          return value.codigo === attribute.codigo_pai;
        }
        return value.codigo_pai;
      });

      if (parentAttr && parentAttr.formaPreenchimento && parentAttr.formaPreenchimento.toUpperCase() === "COMPOSTO") {
        return true;
      }

      if (!parentAttr) {
        const existingIndex = this.dadosAtributos.lista.findIndex(
          (attrF) => attrF.id == attribute.id
        );
        if (existingIndex != -1) {
          this.dadosAtributos.lista.splice(existingIndex, 1);
        }
        return true;
      }

      if (attribute.codigo) {
        var attrCondition = parentAttr.condicionados.find((v) => {
          if (v.atributo.codigo == attribute.codigo) return v;
        });
      } else {
        if (parentAttr && parentAttr.listaSubatributos) {
          var attrCondition = parentAttr.listaSubatributos.find((v) => {
            if (v.codigo_pai == attribute.codigo_pai) return v;
          });
        }
      }
      const isCondition = this.handleCondition(
        parentAttr,
        parentAttr.dbdata[itemId] ? parentAttr.dbdata[itemId].codigo : '',
        attrCondition
      );

      if (
        parentAttr.dbdata[itemId] &&
        parentAttr.dbdata[itemId].codigo &&
        isCondition
      ) {
        return true;
      }

      return false;
    },
    handleCondition(attrParent, valueSelected, attrChild) {
      const evaluateCondition = (value, cond) => {
        const operator = cond.operador;
        const targetValue = cond.valor;

        switch (operator) {
          case '==':
            return value == targetValue;
          case '===':
            return value === targetValue;
          case '!=':
            return value != targetValue;
          case '!==':
            return value !== targetValue;
          case '>':
            return value > targetValue;
          case '<':
            return value < targetValue;
          case '>=':
            return value >= targetValue;
          case '<=':
            return value <= targetValue;
          default:
            throw new Error('Operador desconhecido: ' + operator);
        }
      };

      const evaluateNestedCondition = (value, cond) => {
        const currentResult = evaluateCondition(value, cond);

        if (cond.condicao) {
          const nextResult = evaluateNestedCondition(value, cond.condicao);
          const composition = cond.composicao || '&&';

          if (composition === '||') {
            return currentResult || nextResult;
          } else if (composition === '&&') {
            return currentResult && nextResult;
          } else {
            throw new Error('Composição desconhecida: ' + composition);
          }
        }

        return currentResult;
      };

      let valueSelectedFormat = valueSelected;

      if (attrParent.formaPreenchimento && attrParent.formaPreenchimento.toUpperCase() === 'COMPOSTO') {
        return true;
      }

      if (
        attrParent.formaPreenchimento && attrParent.formaPreenchimento.toUpperCase() === 'BOOLEANO' &&
        (valueSelectedFormat > 0 ||
          valueSelectedFormat == 'Sim' ||
          valueSelectedFormat == 'SIM' ||
          valueSelectedFormat == 'sim')
      ) {
        valueSelectedFormat = 'true';
      }

      if (attrParent.atributoCondicionante) {
        if (
          attrParent.formaPreenchimento && attrParent.formaPreenchimento.toUpperCase() === 'BOOLEANO' &&
          valueSelected == '0'
        ) {
          valueSelectedFormat = 'false';
        }
      }

      return evaluateNestedCondition(valueSelectedFormat, attrChild.condicao);
    },
    openModalHistory(item) {
      this.modalHistoryItem = item;
      this.openModalHomologSelecteds(true);
      if (this.hasBeenEditedAttr == false) {
        setTimeout(() => {
          $("#ModalHistory").modal("show");
        }, 100);
      }
    },
    closeModalHistory() {
      $('#ModalHistory').modal('hide');
      this.modalHistoryItem = null;
    },

    openModalFillInLot(attr) {
      this.modalFillInLotItem = { attr, items: this.getItemsByIds() };
      setTimeout(() => {
        $('#ModalFillInLot').modal('show');
      }, 100);
    },
    getItemsByIds() {
      const filteredItems = this.dadosAtributos.itens.filter((item) =>
        this.selectedItems.includes(item.id_item)
      );
      return filteredItems || [];
    },

    // Método para obter itens selecionados incluindo os não carregados
    getSelectedItemsData() {
      if (this.hasSelectedAllItems && this.allItemsFromNCM.length > 0) {
        // Se selecionou todos os itens da NCM, usar a lista completa
        return this.allItemsFromNCM.filter(item =>
          this.selectedItems.includes(item.id_item)
        );
      } else {
        // Caso contrário, usar apenas os itens carregados
        return this.getItemsByIds();
      }
    },

    // Método para obter apenas os IDs dos itens selecionados
    getSelectedItemIds() {
      return this.selectedItems;
    },
    closeModalFillInLot() {
      this.modalFillInLotItem = null;
      $('#ModalFillInLot').modal('hide');
    },
    async handleFillInLot(data) {
      const { attr: attrFromModal, items, overwriteAll, value } = data;

      this.hasBeenEditedAttr = true;
      this.$emit("dataEdited", true);

      const attrIndex = this.dadosAtributos.lista.findIndex(a => a.codigo === attrFromModal.codigo);
      if (attrIndex === -1) {
        console.error('Atributo não encontrado:', attrFromModal.codigo);
        return;
      }
      const attr = this.dadosAtributos.lista[attrIndex];

      await new Promise(resolve => setTimeout(resolve, 100));

      // Atualizar dbdata
      items.forEach((item) => {
        const { id_item } = item;
        if (!attr.dbdata[id_item]) {
          this.$set(attr.dbdata, id_item, { codigo: '' });
        }
        // Se estiver marcado para sobrescrever todos os valores, ou o valor do item for nulo,
        // atualiza o valor do item com o valor selecionado.
        // Caso contrário, não faz nada.
        if (overwriteAll || !attr.dbdata[id_item].codigo) {
          // Atualiza o dbdata com o novo valor.
          this.$set(attr.dbdata, id_item, { codigo: value.toString() });

          // Atualiza o select e o Bootstrap Select.
          const containerId = `${id_item}_${attr.codigo}`;
          const $select = $(`#${containerId} select`);

          if ($select.length > 0) {
            // Atualiza o valor do select.
            $select.val(value.toString());

            // Atualiza o Bootstrap Select.
            this.updateBootstrapSelect(containerId, value);
          }
        }
      });

      // Verificar colunas filhas
      const newColumns = [];
      const deleteColumns = [];
      const attIsConditions = {};

      const addColumn = (item, parentCode) => {
        if (item && item.codigo) {
          return {
            ...item,
            codigo_pai: parentCode,
            dbdata: item.dbdata || {}
          };
        }
        return null;
      };

      items.forEach((item) => {
        const idItem = item.id_item;

        if (attr.listaSubatributos && attr.listaSubatributos.length > 0) {
          attr.listaSubatributos.forEach(subItem => {
            const column = addColumn(subItem, attr.codigo);
            if (column) {
              if (value) {
                newColumns.push(column);
              } else {
                deleteColumns.push(column.codigo);
              }
            }
          });
        } else if (attr.condicionados && attr.condicionados.length > 0) {
          attr.condicionados.forEach(condItem => {
            if (!condItem.atributo || !condItem.atributo.codigo) return;

            const isCondition = this.handleCondition(attr, value, condItem);

            if (isCondition) {
              const column = addColumn(condItem.atributo, attr.codigo);
              if (column) newColumns.push(column);
            } else {
              if (!attIsConditions[condItem.atributo.codigo]) {
                attIsConditions[condItem.atributo.codigo] = [];
              }

              let shouldKeepColumn = false;
              if (attr.dbdata) {
                Object.keys(attr.dbdata).forEach(key => {
                  const cond = attr.dbdata[key];
                  const isConditionDbdata = this.handleCondition(attr, cond.codigo, condItem);
                  attIsConditions[condItem.atributo.codigo].push(isConditionDbdata);
                  if (isConditionDbdata) {
                    shouldKeepColumn = true;
                  }
                });
              }

              if (!shouldKeepColumn) {
                deleteColumns.push(condItem.atributo.codigo);
              }
            }
          });
        }
      });

      // Processar novas colunas e deleções
      if (newColumns.length > 0 || deleteColumns.length > 0 || Object.keys(attIsConditions).length > 0) {
        const originalIndexMap = {};
        this.dadosAtributos.lista.forEach((attr, index) => {
          originalIndexMap[attr.codigo] = index;
        });

        newColumns.reverse().forEach(newItem => {
          const existingIndex = this.dadosAtributos.lista.findIndex(
            attr => attr.codigo === newItem.codigo
          );

          if (existingIndex === -1) {
            const parentIndex = originalIndexMap[newItem.codigo_pai];
            if (typeof parentIndex !== 'undefined') {
              this.dadosAtributos.lista.splice(parentIndex + 1, 0, newItem);
              Object.keys(originalIndexMap).forEach(key => {
                if (originalIndexMap[key] > parentIndex) {
                  originalIndexMap[key]++;
                }
              });
            }
          }
        });

        if (deleteColumns.length > 0) {
          const columnsToDelete = deleteColumns.filter(codigo => {
            const attr = this.dadosAtributos.lista.find(a => a.codigo === codigo);
            if (!attr) return false;

            const isNeededByAnyItem = Object.values(this.dadosAtributos.itens).some(item => {
              const parentAttr = this.dadosAtributos.lista.find(
                a => a.codigo === attr.codigo_pai
              );
              if (!parentAttr || !parentAttr.dbdata) return false;

              return Object.values(parentAttr.dbdata).some(dbdata => {
                if (!dbdata) return false;
                return this.checkIfColumnIsNeeded(dbdata, attr);
              });
            });

            return !isNeededByAnyItem;
          });

          columnsToDelete.forEach(codigo => {
            const index = this.dadosAtributos.lista.findIndex(
              attr => attr.codigo === codigo
            );
            if (index !== -1) {
              this.dadosAtributos.lista.splice(index, 1);
            }
          });
        }

        this.dadosAtributos.lista.forEach(attr => {
          if (attIsConditions[attr.codigo]) {
            this.$set(attr, 'isConditionallyVisible', true);
            this.$set(attr, 'visibilityConditions', attIsConditions[attr.codigo]);
          }
        });
      }

      this.$set(this.dadosAtributos.lista, attrIndex, { ...attr });

      this.$nextTick(() => {
        this.$forceUpdate();
      });

      this.closeModalFillInLot();
    },
    openModalMoveSelecteds() {
      this.modalMoveSelectedNcm = this.ncmId;
      setTimeout(() => {
        $('#ModalMoveSelecteds').modal('show');
      }, 100);
    },
    closeModalMoveSelecteds() {
      this.modalMoveSelectedNcm = null;
      $('#ModalMoveSelecteds').modal('hide');
    },
    sleep(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },
    async requestDiana(attr, item, mail) {

      let desc = item.descricao_completa ? item.descricao_completa : item.descricao_curta;
      if (item.perguntasRespostas) {
        desc += item.perguntasRespostas;
      }
      if (item.part_number) {
        desc += ' Part Number: ' + item.part_number;
      }

      if (item.marca) {
        desc += ' Marca: ' + item.marca;
      }

      if (item.material_constitutivo) {
        desc += ' Material Constitutivo: ' + item.material_constitutivo;
      }

      if (item.descricao_curta) {
        desc += ' Descrição curta: ' + item.descricao_curta;
      }

      if (item.descricao_mercado_local) {
        desc += ' Descrição resumida: ' + item.descricao_mercado_local;
      }

      const response = await axios.post(`atributos/requestDiana`, {

        "descricao": desc,
        "codigo": attr.codigo,
        "checar_info_na_desc": false,
        "busca_trechos": false,
        "busca_razao": false,
        "request_email": this.email_usuario
      }, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      let resp = response.data;

      if (response.data.valor) {
        this.hasBeenEditedAttr = true;
        this.$emit("dataEdited", true);
      }

      if (resp.valor == false) {
        return 0;
      } else if (resp.valor == true) {
        return 1;
      }

      return response.data.valor;
    },
    processDiana() {
      let promises = [];
      let selectedItems = this.getItemsByIds();
      selectedItems.forEach((item) => {
        let dataId = item.id_item;
        // let trElement = $('tr[data-id="' + dataId + '"]');

        this.dadosAtributos.lista.forEach((attr) => {
          let containerId = `${item.id_item}_${attr.codigo}`;
          let $inputElement = $(`#${containerId} input`);
          let $selectElement = $(`#${containerId} select`);

          let elementoEncontrado = null;
          let valorAtual = null;
          let temValor = false;
          let tipoElemento = '';

          // Identifica o elemento e verifica se tem valor
          if ($inputElement.length > 0) {
            elementoEncontrado = $inputElement;
            valorAtual = elementoEncontrado.val();
            tipoElemento = 'input';
            if (valorAtual != null && valorAtual.trim() !== '') {
              temValor = true;
            }
          } else if ($selectElement.length > 0) {
            elementoEncontrado = $selectElement;
            valorAtual = elementoEncontrado.val();
            tipoElemento = 'select';
            if (valorAtual != null && valorAtual !== '') {
              temValor = true;
            }
          }

          // if (this.attrs_processados_diana) {
          if (!this.attrs_processados_diana.includes(`${item.id_item}_${attr.codigo}`)) {

            if (elementoEncontrado) {
              if (!temValor) {

                this.attrs_processados_diana.push(`${item.id_item}_${attr.codigo}`);

                const $elementoParaAtualizar = elementoEncontrado;
                const currentAttr = attr;
                const currentItemId = item.id_item;

                if (($inputElement.val() != null &&
                  $inputElement.val() != undefined &&
                  $inputElement.val() != '') ||
                  ($selectElement.val() != null &&
                    $selectElement.val != undefined &&
                    $selectElement.val() != '')
                ) {
                  return;
                }

                let promise = this.requestDiana(currentAttr, item)
                  .then((apiValorRecebido) => {

                    this.$nextTick(() => {

                      this.$emit('changedValues', { value: apiValorRecebido, attrCodigo: attr.codigo, itemId: this.idItem });

                      this.handleNewColumns(apiValorRecebido, item.id_item, {});
                    });

                    if (apiValorRecebido !== null &&
                      apiValorRecebido !== undefined &&
                      String(apiValorRecebido).trim() !== '') {

                      const valorAntigo = $elementoParaAtualizar.val();
                      const valorNovoString = String(apiValorRecebido);
                      const attrIndex = this.dadosAtributos.lista.findIndex(a => a.codigo === currentAttr.codigo);

                      if (attrIndex !== -1) {
                        if (!this.dadosAtributos.lista[attrIndex].dbdata) {
                          this.$set(this.dadosAtributos.lista[attrIndex], 'dbdata', {});
                        }
                        if (!this.dadosAtributos.lista[attrIndex].dbdata[currentItemId]) {
                          this.$set(this.dadosAtributos.lista[attrIndex].dbdata, currentItemId, { codigo: '' });
                        }
                        this.$set(this.dadosAtributos.lista[attrIndex].dbdata[currentItemId], 'codigo', valorNovoString);
                      } else {
                        console.warn(`Não foi possível encontrar o atributo ${currentAttr.codigo} nos dados Vue para atualizar.`);
                      }

                      // ATUALIZA O DOM COM JQUERY (Inputs e Selects)
                      if ($elementoParaAtualizar.is('input')) {
                        $elementoParaAtualizar.val(valorNovoString);
                        $elementoParaAtualizar.trigger('input').trigger('change'); // Notifica listeners/Vue
                      } else if ($elementoParaAtualizar.is('select')) {
                        const optionExists = $elementoParaAtualizar.find(`option[value="${valorNovoString}"]`).length > 0;
                        if (optionExists) {
                          $elementoParaAtualizar.val(valorNovoString);
                          if (typeof $elementoParaAtualizar.selectpicker === 'function') {
                            try {
                              $elementoParaAtualizar.selectpicker('refresh');
                              $elementoParaAtualizar.selectpicker('render');
                            } catch (e) { console.error("Erro selectpicker update", e); }
                          }
                          $elementoParaAtualizar.trigger('change');
                        } else {
                          console.warn(`Opção '${valorNovoString}' não existe no select ${containerId}`);
                        }
                      }

                      if ($elementoParaAtualizar.val() == valorNovoString && valorNovoString !== String(valorAntigo)) {
                        $(`#${containerId}`).addClass('valor-atualizado');
                      }

                    } else {
                      console.log(`Valor vazio/inválido recebido da API Diana para ${containerId}`);
                    }
                  })
                  .catch(apiError => {
                    console.error(`Erro na Promise requestDiana para ${containerId}:`, apiError);
                  });

                promises.push(promise);

              }
            }

          }
          //   }
        });
      });

      Promise.all(promises).then(() => {

        swal("Sucesso!", "Sugestão Diana aplicada.", "success");
        $('#loading-overlay').hide();
        return true;
      }).catch(error => {

        console.error('Erro ao processar as promessas:', error);
        // this.isLoading = false;
        $('#loading-overlay').hide();
        return true;
      });

    },
    integraDiana() {
      // Verificar se Diana pode ser usado com a seleção atual
      if (!this.canUseDianaForSelection) {
        swal({
          title: "Atenção!",
          text: "Diana só funciona com itens que estão carregados na tela. Desmarque os itens não carregados ou carregue mais itens.",
          type: "warning",
        });
        return;
      }

      this.attrs_processados_diana = [];
      // this.isLoading = true;
      $('#loading-overlay').show();
      let promises = [];
      let selectedItems = this.getItemsByIds();

      selectedItems.forEach((item) => {
        let dataId = item.id_item;
        // let trElement = $('tr[data-id="' + dataId + '"]');

        this.dadosAtributos.lista.forEach((attr) => {
          let containerId = `${item.id_item}_${attr.codigo}`;
          let $inputElement = $(`#${containerId} input`);
          let $selectElement = $(`#${containerId} select`);
          // let container_input = $(`#${containerId}`);
          let elementoEncontrado = null;
          let valorAtual = null;
          let temValor = false;
          let tipoElemento = '';

          // Identifica o elemento e verifica se tem valor
          if ($inputElement.length > 0) {
            elementoEncontrado = $inputElement;
            valorAtual = elementoEncontrado.val();
            tipoElemento = 'input';
            if (valorAtual != null && valorAtual.trim() !== '') {
              temValor = true;
            }
          } else if ($selectElement.length > 0) {
            elementoEncontrado = $selectElement;
            valorAtual = elementoEncontrado.val();
            tipoElemento = 'select';
            if (valorAtual != null && valorAtual !== '') {
              temValor = true;
            }
          }

          //  if (this.attrs_processados_diana) {
          if (!this.attrs_processados_diana.includes(`${item.id_item}_${attr.codigo}`)) {

            if (elementoEncontrado) {

              if (!temValor) {

                const $elementoParaAtualizar = elementoEncontrado;
                const currentAttr = attr;
                const currentItemId = item.id_item;


                if (($inputElement.val() != null && $inputElement.val() != undefined && $inputElement.val() != '') ||
                  ($selectElement.val() != null && $selectElement.val != undefined && $selectElement.val() != '')
                ) {
                  return;
                }


                let promise = this.requestDiana(currentAttr, item)
                  .then((apiValorRecebido) => {
                    this.attrs_processados_diana.push(`${item.id_item}_${attr.codigo}`);

                    this.$nextTick(() => {
                      this.$emit('changedValues', { value: apiValorRecebido, attrCodigo: attr.codigo, itemId: item.id_item });

                      // Prepara os dados para handleNewColumns
                      const newItens = [];
                      const deleteItens = [];
                      const arrayIsConditions = {};

                      // Verifica se o atributo tem condicionados
                      if (attr.condicionados && attr.condicionados.length > 0) {
                        attr.condicionados.forEach(condicionado => {
                          if (this.handleCondition(attr, apiValorRecebido, condicionado)) {
                            // Adiciona o atributo com a propriedade codigo_pai definida
                            const newItem = {
                              ...condicionado.atributo,
                              codigo_pai: attr.codigo, // Define o código do pai
                              dbdata: condicionado.atributo.dbdata || {}
                            };
                            newItens.push(newItem);
                          }
                        });
                      }

                      this.handleNewColumns(0, 0, {
                        newItens,
                        deleteItens,
                        arrayIsConditions,
                        idItem: item.id_item
                      });
                    });

                    if (apiValorRecebido !== null && apiValorRecebido !== undefined && String(apiValorRecebido).trim() !== '') {

                      const valorAntigo = $elementoParaAtualizar.val();
                      const valorNovoString = String(apiValorRecebido);

                      const attrIndex = this.dadosAtributos.lista.findIndex(a => a.codigo === currentAttr.codigo);
                      if (attrIndex !== -1) {

                        if (!this.dadosAtributos.lista[attrIndex].dbdata) {
                          this.$set(this.dadosAtributos.lista[attrIndex], 'dbdata', {});
                        }
                        if (!this.dadosAtributos.lista[attrIndex].dbdata[currentItemId]) {
                          this.$set(this.dadosAtributos.lista[attrIndex].dbdata, currentItemId, { codigo: '' });
                        }


                        //  $(`#${item.id_item}_${attr.codigo} select`).selectpicker('val', valorNovoString);

                        if ($selectElement.is('select')) {
                          const optionExists = $selectElement.find(`option[value="${valorNovoString}"]`).length > 0;
                          if (optionExists) {
                            $selectElement.val(valorNovoString);
                            if (typeof $selectElement.selectpicker === 'function') {
                              try {
                                $selectElement.selectpicker('refresh');
                                $selectElement.selectpicker('render');
                              } catch (e) { console.error("Erro selectpicker update", e); }
                            }
                            $selectElement.trigger('change'); // Notifica listeners/Vue DEPOIS de atualizar UI do plugin
                          } else {
                            console.warn(`Opção '${valorNovoString}' não existe no select ${containerId}`);
                          }
                        }


                        this.$set(this.dadosAtributos.lista[attrIndex].dbdata[currentItemId], 'codigo', valorNovoString);
                      } else {
                        console.warn(`Não foi possível encontrar o atributo ${currentAttr.codigo} nos dados Vue para atualizar.`);
                      }

                      if ($elementoParaAtualizar.is('input')) {
                        $elementoParaAtualizar.val(valorNovoString);
                        $elementoParaAtualizar.trigger('input').trigger('change'); // Notifica listeners/Vue
                      } else if ($elementoParaAtualizar.is('select')) {

                        const optionExists = $elementoParaAtualizar.find(`option[value="${valorNovoString}"]`).length > 0;
                        if (optionExists) {
                          $elementoParaAtualizar.val(valorNovoString);
                          if (typeof $elementoParaAtualizar.selectpicker === 'function') {
                            try {
                              $elementoParaAtualizar.selectpicker('refresh');
                              $elementoParaAtualizar.selectpicker('render');
                            } catch (e) { console.error("Erro selectpicker update", e); }
                          }
                          $elementoParaAtualizar.trigger('change'); // Notifica listeners/Vue DEPOIS de atualizar UI do plugin
                        } else {
                          console.warn(`Opção '${valorNovoString}' não existe no select ${containerId}`);
                        }
                      }

                      if ($elementoParaAtualizar.val() == valorNovoString && valorNovoString !== String(valorAntigo)) {
                        $(`#${containerId}`).addClass('valor-atualizado'); // Usa a classe deste componente
                      }

                    } else {
                      console.log(`Valor vazio/inválido recebido da API Diana para ${containerId}`);
                    }
                  })
                  .catch(apiError => {
                    console.error(`Erro na Promise requestDiana para ${containerId}:`, apiError);
                  });
                promises.push(promise);


              } else {
                //   elementoEncontrado.removeClass('valor-atualizado');
              }
            } else {
              console.warn(`Nenhum input ou select encontrado para o seletor base #${containerId}`);
            }

          }
          // }
        });
      });

      Promise.all(promises)
        .then(() => {
          // this.isLoading = false;

          this.processDiana();
        }).finally(() => {
          // swal("Sucesso!", "Sugestão Diana aplicada.", "success");
          // // this.attrs_processados_diana = [];
          //   // this.isLoading = false;
          //   $('#loading-overlay').hide();

        })

    },

    openModalHomologSelecteds(modalActions = false, homologar = false) {
      const selectedItems = modalActions
        ? [this.modalHistoryItem]
        : this.getSelectedItemsData();

      let itensSelecionados = selectedItems;
      const itensAttrPendentes = new Set();
      selectedItems.forEach((item) => {
        let dataId = item.id_item;
        let trElement = $('tr[data-id="' + dataId + '"]');
        let partNumber = trElement.data("part_number");
        let idItem = trElement.data("id");

        const nomesCamposVazios = new Set();
        const mensagensDeErro = [];
        if (trElement.length > 0) {
          let camposObrigatorios = trElement.find("input[required], select[required]");

          if (camposObrigatorios.length > 0) {
            camposObrigatorios.each(function () {
              const campo = $(this);
              const valorSelecionado = campo.val();
              const apresentacao = campo.data('apresentacao') || 'não definido';

              if (!valorSelecionado) {
                if (!nomesCamposVazios.has(apresentacao)) {
                  mensagensDeErro.push(
                    `Campo obrigatório <b>'${apresentacao}'</b> está vazio. <br>`
                  );
                  nomesCamposVazios.add(apresentacao);
                }
              }
            });

            if (mensagensDeErro.length > 0) {
              const mensagensFormatadas = mensagensDeErro.join("");

              if (this.homologarSemObrigatorios == 0) {
                itensAttrPendentes.add(idItem);
                item.obrigatorio_vazio = true;

                // Se a tentativa de abrir o modal vier da homologação selecionados, mostra uma mensagem de alerta
                if (homologar) {
                  swal({
                    title: "Atenção!",
                    text: mensagensFormatadas,
                    type: "warning",
                    confirmButtonText: "OK",
                    allowOutsideClick: false,
                  });

                  throw new Error(mensagensFormatadas);
                }
              }

              if (this.homologarSemObrigatorios == 1) {
                item.obrigatorio_vazio = true;
              }
            }
          }
        }
      });

      let itensHomologar = [];
      itensSelecionados.forEach((item) => {
        if (!itensAttrPendentes.has(Number(item.id_item))) {
          itensHomologar.push(item);

        }

        if (item.obrigatorio_vazio) {
          itensHomologar.has_obrigatorio_vazio = true;
        }
      });
      let alertAttr = false;
      if (this.hasBeenEditedAttr) {
        alertAttr = true;
        swal({
          title: 'Atenção!',
          text: 'Você deseja salvar os atributos que foram editados?',
          type: 'warning',
          confirmButtonText: 'Sim',
          cancelButtonText: 'Não',
          showConfirmButton: true,
          showCancelButton: true,
          allowOutsideClick: false,
        }).then((value) => {
          if (value) {
            this.hasBeenEditedAttr = false;
            this.save()
              .then(() => {
                // Chamar novamente a função como recursividade
                this.openModalHomologSelecteds(modalActions);
              })
              .then(() => {
                this.modalHomologSelectedItem = {
                  ncm: this.ncmId,
                  items: itensHomologar,
                };

                // Mostra o modal
                setTimeout(() => {
                  $('#ModalHistory').modal('show');
                }, 200);
              })
              .catch((error) => {
                console.error('Erro ao salvar:', error);
              });
          }
        });
      } else {
        this.modalHomologSelectedItem = {
          ncm: this.ncmId,
          items: itensHomologar,
        };

        if (!modalActions && !alertAttr) {
          setTimeout(() => {
            $("#ModalHomologSelecteds").modal("show");
          }, 100);
        }
      }
    },
    closeModalHomologSelecteds() {
      this.modalHomologSelectedItem = null;
      $('#ModalHomologSelecteds').modal('hide');
    },
    refreshTooltip(element, attr) {
      if (!element || !attr) {
        console.log('Missing element or attr:', { element, attr });
        return;
      }

      // Usar requestAnimationFrame para otimizar performance
      requestAnimationFrame(() => {
        const $element = $(element);

        // Destruir tooltip existente se houver
        if ($element.data('bs.tooltip')) {
          $element.tooltip('destroy');
        }

        // Configurar novo tooltip
        $element.tooltip({
          html: true,
          container: 'body',
          placement: 'auto',
          trigger: 'hover',
          title: `<strong> Descrição: </strong> ${attr.nomeApresentacao}`
        });
      });
    },
    initializeTooltips() {
      // Usar requestAnimationFrame para otimizar performance
      requestAnimationFrame(() => {
        $('[data-toggle="tooltip"]').tooltip({
          html: true,
          container: 'body',
          placement: 'auto',
          trigger: 'hover'
        });
      });
    }
  },
};
</script>

<style scoped>
button:focus {
  outline: none;
  box-shadow: none;
}

.table-responsive {
  overflow-x: auto;
}

.table-atribute {
  padding: 0;
  overflow-x: auto;
  min-height: 250px;
  background-color: #eaeef2;
}

.tooltip-trigger {
  display: inline-block;
  position: relative;
}

.tooltip {
  position: absolute !important;
  z-index: 9999 !important;
  pointer-events: none;
}

.tooltip-inner {
  max-width: 300px;
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: yellow !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  text-align: left !important;
}

.table {
  margin: 0 !important;
  border-spacing: 0;
  border-collapse: separate;
}

.table thead {
  background: #337ab7;
  color: white;
}

.odd-item tr td {
  background: #f5f8fb;
}

.table-atribute th {
  background: #337ab7;
}

.table-atribute td {
  background: white;
}

.table-atribute th,
.table-atribute td {
  border: none;
  vertical-align: middle !important;
}

.table-atribute tr th:nth-child(1),
.table-atribute tr td:nth-child(1) {
  position: sticky;
  left: 0px;
  width: 50px;
  min-width: 50px;
  max-width: 50px;
  z-index: 1000;
  border-left: 1px solid #ddd;
}

.table-atribute th:nth-child(2),
.table-atribute td:nth-child(2) {
  position: sticky;
  left: 50px;
  width: 150px;
  min-width: 150px;
  max-width: 150px;
  z-index: 1000;
}

.table-atribute th:nth-child(3),
.table-atribute td:nth-child(3) {
  position: sticky;
  left: 200px;
  width: 230px;
  min-width: 230px;
  max-width: 230px;
  z-index: 1000;

}

.table-atribute th:nth-child(4),
.table-atribute td:nth-child(4) {
  position: sticky;
  left: 430px;
  width: 30px;
  min-width: 30px;
  max-width: 30px;
  z-index: 1000;
  background-color: #337ab7;
  border-right: 1px solid #ddd;
}

.table-atribute td:nth-child(4) {
  background-color: white;
}

.odd-item tr td:nth-child(4) {
  background: #f5f8fb;
}

.table-atribute th:nth-child(5),
.table-atribute td:nth-child(5) {
  border-left: 1px solid #ddd;
}

.sticky-actions {
  position: sticky;
  right: 0;
  background-color: white;
  z-index: 1000;
  border-left: 1px solid #ddd !important;
  border-right: 1px solid #ddd !important;
  padding: 0 15px;
}

.dynamic-column {
  background-color: #296292 !important;
  min-width: 200px;
  width: 200px;
}

.dynamic-column-th {
  display: flex;
  align-items: center;
  gap: 10px;
}

.dynamic-column-description {
  background-color: #337ab7 !important;
}

.dynamic-column-actions {
  background-color: #337ab7 !important;
}

.dynamic-column-actions-th {
  display: flex;
  align-items: center;
  gap: 10px;
}

.col-description {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.centralize {
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon {
  background-color: white;
  color: black;
  padding: 8px;
  border: 1px solid #cccccc;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon:focus {
  outline: none;
  box-shadow: none;
}

.btn-icon-fill-lot {
  background-color: #337ab7;
  color: white;
  padding: 4px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-icon-fill-lot:focus {
  outline: none;
  box-shadow: none;
}

hr {
  margin: 0px 0 5px 0;
  border-top: 1px solid #000;
}

.complete-description {
  margin: 0 0 10px 0 !important;
}

.btn-load-more {
  background-color: white;
  border: 1px solid #cccccc;
  font-weight: 600;
  font-size: 16px;
}

.btn-load-more:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.buttons-actions-container {
  display: flex;
  justify-content: space-between;
}

.buttons-actions-container div {
  display: flex;
  gap: 15px;
}

.form-check-input {
  accent-color: #007bff;
}

/* Estilos do tooltip */
.tooltip {
  position: absolute !important;
  z-index: 9999 !important;
  pointer-events: none;
  max-width: 300px;
}

.tooltip-inner {
  background-color: rgba(0, 0, 0, 0.85) !important;
  color: yellow !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  white-space: normal !important;
  text-align: left !important;
}

.tooltip-arrow {
  display: none;
}
</style>
