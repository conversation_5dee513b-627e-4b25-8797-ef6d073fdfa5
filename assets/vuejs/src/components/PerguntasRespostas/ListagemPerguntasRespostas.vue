<template>
  <div class="row">
    <div v-if="items.length">
      <div
        class="col-md-12"
        v-for="(item, index) in items"
        :key="item.id"
        style="margin-bottom: 15px"
      >
        <div class="row">
          <div class="col-md-12">
            <label for="nome" class="d-block">
              {{ index + 1 }} - {{ item.pergunta }}
              <span data-toggle="tooltip" :title="'Grupo de Perguntas: ' + item.grupo_nome_novo" style="margin-left: 4px;">
                <i class="glyphicon glyphicon-info-sign" style="color: #3276B1"></i>
              </span>
            </label>
          </div>
        </div>
        <div class="row">
          <div class="" style="padding-right: 10px">
            <div
              :class="item.arquivos.length > 0 ? 'col-md-10' : 'col-md-11'"
              style="padding-right: 20px"
            >
              <textarea v-model="item.resposta"  class="form-control size-textarea" aria-label="..."  rows="3" cols="90" readonly >  </textarea>

            </div>
          </div>
    
          <div
            v-if="permissionDelete.data == 1 && item.resposta"
            class="col-md-1 input-group-btn"
            style="padding-left: 6px"
          >
            <button
              :class="'btn ' + 'btn-danger'"
              type="button"
              @click="deleteQuestion(item.id_pergunta, item.pergunta, item.ids)"
              style="outline: none"
              v-if="permissao_excluir_perguntas"
            >
              <i class="glyphicon glyphicon-trash"></i>
            </button>
          </div>

          <div class="col-md-1" style="padding-left: 40px">
            <div
              class="dropdown"
              style="display: inline"
              v-if="item.arquivos.length > 0"
            >
              <button
                class="btn btn-secondary dropdown-toggle"
                style="margin-bottom: 5px"
                type="button"
                id="dropdownMenuButton"
                data-toggle="dropdown"
                aria-haspopup="true"
                aria-expanded="false"
              >
                <i class="glyphicon glyphicon-download"></i>
              </button>
              <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                <a
                  class="dropdown-item btn-download"
                  v-for="arquivo in item.arquivos"
                  :title="arquivo.nome"
                  :key="arquivo.id"
                  :href="`${baseUrl}assets/respostas/${arquivo.nome}`"
                  download
                >
                  {{ arquivo.nome }}
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="col-md-12">
      <p>Nenhuma informação encontrada</p>
    </div>
  </div>
</template>
<script>
import _ from "lodash";

export default {
  data() {
    return {
      items: [],
      permissao_excluir_perguntas: false,
      permissao_grupo_perguntas: false
    };
  },
  props: {
    partnumber: {
      required: true,
      default: Number | String,
    },
    estabelecimento: {
      required: false,
      default: String,
    },
    baseUrl: {
      required: false,
      default: String,
    },
    permissionDelete: {
      required: true,
      type: String,
    },
  },
  methods: {
    getPermissao() {
      return this.$http.get('pr/perguntas/getPermissao');
    },
    async permissaoDeletarPerguntas() {
      let response = await this.$http.get('pergunta/permissaoDeletarPerguntas', {});
      this.permissao_excluir_perguntas = response.data;
    },
    async permissaoGrupoPerguntas() {
      let response = await this.$http.get('pergunta/permissaoGrupoPerguntas', {});
      this.permissao_grupo_perguntas = response.data;
    },
    getHistoricoItem() {
      this.$http
        .get("pr/respostas/getHistoricoItem", {
          params: {
            partnumber: this.partnumber,
            estabelecimento: this.estabelecimento,
          },
        })
        .then(({ data }) => {
          if (data && data.data) {
            this.items = data.data;
          } else {
            this.items = [];
          }
        })
        .catch(error => {
          console.error("Erro ao buscar o histórico de perguntas e respostas:", error);
          this.items = [];
        });
    },
    deleteQuestion(id, pergunta_item, ids_perguntas) {
      let partnumbers = {
        id_pergunta: id,
        estabelecimento: this.estabelecimento,
        part_number: this.partnumber,
        pergunta: pergunta_item,
        tipo_exclusao: "Resposta",
        ids: ids_perguntas
      };

      swal({
        title: "Atenção!",
        text: "Você deseja excluir a Resposta da <br> " + pergunta_item,
        type: "warning",
        confirmButtonText: "OK",
        cancelButtonText: "Cancelar",
        showConfirmButton: true,
        showCancelButton: true,
        allowOutsideClick: false,
      }).then((value) => {
        if (value) {
          let result;
          result = this.$http
            .get("pr/perguntas/deletarPergunta", {
              params: {
                partnumbers,
              },
            })
            .then((response) => {
              swal(
                  'OK!',
                  'A resposta foi removida com sucesso.',
                  'success'
              );
              this.getHistoricoItem();
            });
        // window.location.reload();
        }
      });
    },
  },
  async mounted() {
    this.getHistoricoItem();
    this.permissionDelete = await this.getPermissao();
    this.permissaoDeletarPerguntas();
    this.permissaoGrupoPerguntas();
  },
};
</script>

<style scoped>
.form-group.files .form-control {
  padding: 5px 4px;
}

.btn-download {
  display: block;
  padding: 5px 12px;
  border-bottom: 1px solid rgb(211, 211, 211);
}

.btn-download:last-child {
  display: block;
  padding: 5px 12px;
  border-bottom: none;
}

.form-control .input-file {
  padding: 5px 5px !important;
}

.size-textarea{
    max-width: 500px !important;
    max-height: 80px !important;
}
</style>