<div class="modal fade" id="transferirResponsavel" tabindex="-1" role="dialog" aria-labelledby="transferirResponsavel" aria-hidden="true" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" style="display: inline !important">Transferir Responsável</h3>

                <button type="button" class="close close-transfer-modal" data-dismiss="modal" aria-label="Close" style="margin-top: 6px;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>

            <div class="modal-body d-flex" style="flex-direction: column;">

                <?php if ($hasOwner) : ?>
                    <div class="form-group col-md-6" style="margin-bottom: 15px">
                        <label>Tipo de Responsável:</label>
                        <div>
                            <input type="radio" name="responsavel-tipo" id="user" value="user" />
                            <label for="user" class="radio-input1">Usuário</label>
                            <input type="radio" name="responsavel-tipo" id="owner" value="owner" checked />
                            <label for="owner" class="radio-input1">Owner</label>
                        </div>
                    </div>

                    <div class="form-group" id="ownerSelect-transfer">
                        <label>Informe o novo owner responsável</label>

                        <select 
                        class="form-control selectpicker" data-selected-text-format="count > 0" data-count-selected-text="Owner ({0})" data-live-search="true" data-actions-box="true" data-select-all-text="Todos" data-deselect-all-text="Nenhum" name="ownerPergunta[]" title="Selecionar" id="ownerPergunta-transfer">
                            <?php if (count($ownersToTransfer) > 0) : ?>
                                <?php foreach ($ownersToTransfer as $item) : ?>
                                    <option value="<?php echo $item->codigo; ?>">
                                        <?php echo $item->codigo; ?> - <?php echo $item->descricao; ?> - <?php echo $item->nomes; ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>

                        <small>Somente perguntas pendentes serão transferidas.</small>

                    </div>

                    <div class="form-group" style="margin-bottom: 10px; display: none;" id="userSelect-transfer">
                        <label class="d-block">Informe o novo usuário responsável</label>
    
                        <select 
                            multiple data-selected-text-format="count > 1" 
                            data-count-selected-text="Responsáveis ({0})"
                            class="selectpicker" 
                            data-live-search="true"
                            title="Selecionar"
                            id="responsavel-transfer"
                        >
                            <?php if(count($responsaveis_ativos) > 0): ?>
                                <option disabled>
                                    <span class="label label-success" style="font-size: 12px; border-radius: 10px;">Usuários ativos</span>
                                </option>
                                <?php foreach($responsaveis_ativos as $responsavel): ?>
                                    <?php if($responsavel->id_usuario != sess_user_id()): ?>
                                        <option style="padding-left: 32px;" value="<?php echo $responsavel->id_usuario?>"><?php echo $responsavel->nome;?></option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
    
                        <small>Somente perguntas pendentes serão transferidas.</small>
                    </div>
                <?php else: ?>
                    <div class="form-group" style="margin-bottom: 10px;">
                        <label class="d-block">Informe o novo usuário responsável</label>
    
                        <select 
                            multiple data-selected-text-format="count > 1" 
                            data-count-selected-text="Responsáveis ({0})"
                            class="selectpicker" 
                            data-live-search="true"
                            title="Selecionar"
                            id="responsavel"
                        >
                            <?php if(count($responsaveis) > 0): ?>
                                <option disabled>
                                    <span class="label label-success" style="font-size: 12px; border-radius: 10px;">Usuários ativos</span>
                                </option>
                                <?php foreach($responsaveis as $responsavel): ?>
                                    <?php if($responsavel->id_usuario != sess_user_id()): ?>
                                        <option style="padding-left: 32px;" value="<?php echo $responsavel->id_usuario?>"><?php echo $responsavel->nome;?></option>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
    
                        <small>Somente perguntas pendentes serão transferidas.</small>
                    </div>
                <?php endif; ?>

            </div>

            <div class="modal-footer">
                <button class="btn btn-default btn-cancel-transfer" data-dismiss="modal" aria-label="Close">
                    Cancelar
                </button>

                <a class="btn btn-primary btn-confirm-transfer">
                    <div class="spinner-border white" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <i class="glyphicon glyphicon-transfer glyphicon-transfer-inside-modal"></i> Transferir
                </a>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
    $(function() {
        function clearSelection(selectId) {
        $('#' + selectId).val([]).selectpicker('refresh');
        }

        // Escuta o evento de mudança nos inputs radio
        $('input[name="responsavel-tipo"]').on('change', function() {
            // Verifica qual opção foi selecionada
            var selecionadoTiṕo = $(this).val();

            // Mostra o select correspondente e esconde o outro
            if (selecionadoTiṕo === 'user') {
                $('#userSelect-transfer').show();
                $('#ownerSelect-transfer').hide();
                clearSelection('ownerPergunta-transfer');
            } else if (selecionadoTiṕo === 'owner') {
                $('#ownerSelect-transfer').show();
                $('#userSelect-transfer').hide();
                clearSelection('responsavel-transfer');
            }
        });

        let idResponsavel = null;
        let partnumber = null;
        let estabelecimento = null;
        let ownerResponsavel = null;

        $(".btn-confirm-transfer").hide();
        $(".spinner-border.white").hide();

        $(".btn-transfer").on("click", function() {
            partnumber = $(this).attr("data-part-number"); 
            estabelecimento = $(this).attr("data-estabelecimento"); 
        });

        $("#responsavel").on("change", function() {
            $(".btn-confirm-transfer").show();
            idResponsavel = $(this).val();
        });

        $("#ownerPergunta-transfer").on("change", function() {
            $(".btn-confirm-transfer").show();
            ownerResponsavel = $(this).val();
        });

        $("#responsavel-transfer").on("change", function() {
            $(".btn-confirm-transfer").show();
            idResponsavel = $(this).val();
        });

        $(".btn-confirm-transfer").on("click", function() {
            $(".close-transfer-modal").hide();
            $(".btn-cancel-transfer").hide();
            $(".glyphicon-transfer-inside-modal").hide();
            
            $(".spinner-border.white").show();
            
            $(".btn-confirm-transfer").attr("disabled", true);

            $.post("<?php echo base_url("controle_pendencias/transferirPerguntasPendentes")?>", {
                partnumber,
                idResponsavel,
                estabelecimento,
                ownerResponsavel,
                tipoResponsavel: $('input[name="responsavel-tipo"]:checked').val()
            }).done((data) => {
              //  console.log(data);

              swal({
                title: "Sucesso",
                text: "Perguntas transferidas com sucesso!",
                icon: "success",
                button: "Fechar",
                }).then(() => {
                    location.reload();
                });
    
            });
        });
    });
</script>
 
<style type="text/css">
    #transferirResponsavel .btn-group.bootstrap-select {
        width: 100%;
    }

    .radio-input1 {
    margin-right: 25px;
    }
</style>